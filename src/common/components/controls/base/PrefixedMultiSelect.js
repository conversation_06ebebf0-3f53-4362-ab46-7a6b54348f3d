import classnames from 'classnames';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Fragment } from 'react';

import translatePropTypes from '../../../translatePropTypes';
import withWindowScroll from '../../../utils/withWindowScroll';
import Scroll from '../../other/Scroll';
import Empty from '../../utils/Empty';
import FormControlErrorMessage from '../../utils/FormControlErrorMessage';
import FormControlInfoMessage from '../../utils/FormControlInfoMessage';
import withTranslations from '../../utils/Translations/withTranslations';
import AnimatedTitle from './AnimatedTitle';
import SearchField from './SearchField';
import style from './SelectBox/SelectBox.scss';

@withTranslations
@withWindowScroll
export default class PrefixedMultiSelect extends React.PureComponent {
  static propTypes = {
    placeholder: PropTypes.string.isRequired,
    value: PropTypes.array,
    onChange: PropTypes.func.isRequired,
    options: PropTypes.array,
    groupTitleKeys: PropTypes.array,
    itemTitlePropName: PropTypes.string,
    itemValuePropName: PropTypes.string,
    isRequired: PropTypes.bool,
    isDisabled: PropTypes.bool,
    errorMessage: PropTypes.string,
    orderOptionsBy: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.arrayOf(PropTypes.string),
    ]),
    renderHelpBlock: PropTypes.func,
    isFilterBar: PropTypes.bool,
    hasSelectAll: PropTypes.bool,
    hasLiveSearch: PropTypes.bool,
    onChangeQuery: PropTypes.func,
    infoMessageAlign: PropTypes.oneOf(['left', 'right']),
    infoMessage: PropTypes.string,
    hasNumericOptions: PropTypes.bool,
    maxNumberToShow: PropTypes.number,
    itemRenderer: PropTypes.func,
    className: PropTypes.string,
    ...withWindowScroll.props,
    ...translatePropTypes,
  };

  static defaultProps = {
    value: [],
    options: null,
    groupTitleKeys: null,
    itemTitlePropName: 'name',
    itemValuePropName: 'value',
    isRequired: false,
    isDisabled: false,
    errorMessage: null,
    orderOptionsBy: null,
    renderHelpBlock: null,
    isFilterBar: false,
    hasSelectAll: true,
    hasLiveSearch: true,
    infoMessageAlign: 'right',
    infoMessage: null,
    onChangeQuery: null,
    hasNumericOptions: false,
    maxNumberToShow: 2,
    itemRenderer: null,
    className: '',
  };

  state = {
    isLabelVisible: this.props.value?.length > 0,
    isOpen: false,
    isSelectedAll: this.isSelectedAll,
    search: '',
  };

  componentDidMount() {
    this.props.calculateDropup(this.controlRef);
    this.props.setScrollHandler(this.handleSizeChange);
  }

  componentDidUpdate(prevProps, prevState) {
    const { isOpen } = this.state;
    const { isOpen: wasOpen } = prevState;

    if (isOpen && !wasOpen) {
      this.props.setBodyClickHandler(this.closeMenu, this.containerRef);

      this.props.calculateDropup(this.controlRef);
      this.props.calculateFixedBound({
        setHeight: true,
        hasSearch: this.props.hasLiveSearch,
        controlRef: this.controlRef,
        style: { margin: this.props.isFilterBar ? '-3px -8px' : undefined },
      });
    }

    if (!isOpen && wasOpen) {
      this.props.resetBodyClickHandler();
    }

    if (this.props.value !== prevProps.value) {
      this.refreshLabelVisible();
      // eslint-disable-next-line react/no-did-update-set-state
      this.setState({
        isSelectedAll: this.isSelectedAll,
      });
    }
  }

  refreshLabelVisible() {
    this.setState({ isLabelVisible: this.props.value?.length > 0 });
  }

  handleSizeChange = e => {
    if (
      this.containerRef &&
      (e.target === window || e.target.contains(this.containerRef))
    ) {
      if (this.state.isOpen) {
        this.props.calculateFixedBound({
          setHeight: false,
          hasSearch: this.props.hasLiveSearch,
          controlRef: this.controlRef,
          style: { margin: this.props.isFilterBar ? '-3px -8px' : undefined },
        });
      } else {
        this.props.calculateDropupDebounced(this.controlRef);
      }
    }
  };

  getOptionValue(option) {
    const { itemValuePropName } = this.props;
    return option[itemValuePropName];
  }

  getOptionTitle(option) {
    const { itemTitlePropName } = this.props;
    return option[itemTitlePropName];
  }

  getOptionPrefix = option => option.prefix;

  getIsSelected = option => {
    const { value } = this.props;
    return _.some(value, el => el === option);
  };

  onChange = options => {
    const { onChange, hasNumericOptions } = this.props;
    if (hasNumericOptions) {
      options = options.map(option => +option);
    }
    onChange([...options]);
  };

  handleClick = (value, isSelected) => e => {
    e.preventDefault();
    const { value: valueProp = [] } = this.props;
    const nextValue = isSelected
      ? _.filter(valueProp, el => el !== value)
      : [...valueProp, value];
    this.onChange(nextValue);
  };

  get buttonText() {
    const {
      placeholder,
      isRequired,
      t,
      value,
      options,
      maxNumberToShow,
      itemTitlePropName,
    } = this.props;
    const renderedPlaceholder = isRequired ? `${placeholder} *` : placeholder;

    const flattenOptions = [].concat(...options);

    if (
      value?.length &&
      value.length > 1 &&
      value.length === flattenOptions.length
    ) {
      return t('All selected');
    }

    const selected = [];
    _.forEach(value, v => {
      const option = flattenOptions.find(opt => this.getOptionValue(opt) === v);
      if (option) {
        selected.push({
          [itemTitlePropName]: this.getOptionTitle(option),
          prefix: this.getOptionPrefix(option),
        });
      }
    });

    if (selected.length > maxNumberToShow) {
      return `${selected.length} ${t('selected')}`;
    }

    if (selected.length) {
      return selected.map((option, i, arr) => {
        const divider = arr[i + 1] ? ', ' : ' ';
        return (
          <Fragment key={`${this.getOptionTitle(option)}-${i + 1}`}>
            {this.renderOptionLabel(option, 'ml-5 mr-5')}
            {divider}{' '}
          </Fragment>
        );
      });
    }

    return <span className="text-muted">{renderedPlaceholder}</span>;
  }

  renderOption = (option, index) => {
    const value = this.getOptionValue(option);
    const isSelected = this.getIsSelected(value);

    return (
      <Fragment key={`option-${index}-${value}`}>
        {option.divider && <li className="divider" />}

        {option.header && (
          <li className="text-center" title={option.header}>
            {option.header}
          </li>
        )}

        <li className={classnames({ active: isSelected }, option.className)}>
          <a onClick={this.handleClick(value, isSelected)}>
            <label className="checkbox">
              <div className="checker">
                <span
                  className={classnames(style.colorFix, {
                    checked: isSelected,
                  })}
                >
                  <input type="checkbox" value={value} />
                </span>
              </div>
              {this.renderOptionLabel(option)}
            </label>
          </a>
        </li>
      </Fragment>
    );
  };

  renderOptions = options => {
    const { orderOptionsBy } = this.props;
    const orderedOptions = _.orderBy(options, orderOptionsBy);
    return orderedOptions.map(this.renderOption);
  };

  renderSelectBody = (options, title) => {
    if (!title) return this.renderOptions(options);

    return (
      <Fragment key={title}>
        <li key={title} className="multiselect-item multiselect-group">
          <label>{title}</label>
        </li>
        {this.renderOptions(options)}
      </Fragment>
    );
  };

  get infoMessage() {
    const { infoMessage, infoMessageAlign } = this.props;
    if (!infoMessage) {
      return null;
    }

    return (
      <FormControlInfoMessage
        message={infoMessage}
        messageAlign={infoMessageAlign}
      />
    );
  }

  get errorMessage() {
    const { errorMessage } = this.props;
    if (!errorMessage) {
      return null;
    }
    return <FormControlErrorMessage errorMessage={errorMessage} />;
  }

  handleToggleMenu = () => {
    this.setState(({ isOpen }) => ({ isOpen: !isOpen }));
  };

  getOptionItem = option => {
    const { itemRenderer } = this.props;
    const item = itemRenderer
      ? itemRenderer(option)
      : this.getOptionTitle(option);
    return item;
  };

  renderOptionLabel = (option, classes) => {
    const item = this.getOptionItem(option);
    const prefix = this.getOptionPrefix(option);

    return (
      <>
        {prefix && (
          <span
            className={classnames(
              'priority-menu-item no-margin-top left-inherit',
              classes,
              {
                first: prefix === 'P',
                second: prefix !== 'P',
              },
            )}
            title={item}
          />
        )}
        <span className={classnames({ 'm-l-30': prefix })} title={item}>
          {item}
        </span>
      </>
    );
  };

  handleInputChange = search => {
    this.setState({ search });
    const { onChangeQuery } = this.props;
    onChangeQuery && onChangeQuery(search);
  };

  handleSearchReset = () => this.handleInputChange('');

  get liveSearchInput() {
    if (!this.props.hasLiveSearch) {
      return null;
    }

    const { search } = this.state;
    const { dropup } = this.props;

    return (
      <SearchField
        isInTable
        inputClassName={style.posUnset}
        inputProps={{ autoFocus: true }}
        isDebouncingEnabled={false}
        value={search}
        widthClassName={classnames('searchbox', {
          'display-inline-block no-padding-bottom w-100': dropup,
        })}
        onChange={this.handleInputChange}
        onReset={this.handleSearchReset}
      />
    );
  }

  handleSelectAll = isSelected => e => {
    e.preventDefault();
    const { value: valueProp } = this.props;

    const flattenOptions = [].concat(...this.options);
    const value = flattenOptions.map(opt => this.getOptionValue(opt));

    const isDifferent =
      (value?.length !== valueProp.length && isSelected) ||
      (value?.length === valueProp.length && !isSelected);

    if (isDifferent) {
      isSelected ? this.onChange(value) : this.onChange([]);
      this.setState({ isLabelVisible: isSelected });
    }

    this.setState({ isSelectedAll: isSelected });
  };

  get selectAllButton() {
    const { t, hasSelectAll } = this.props;
    if (!hasSelectAll) {
      return null;
    }
    const { isSelectedAll } = this.state;

    return (
      <li
        key="selectAll"
        className={classnames('multiselect-item multiselect-all', {
          active: this.isSelectedAll,
        })}
      >
        <a
          className="multiselect-all"
          onClick={this.handleSelectAll(!isSelectedAll)}
        >
          <label className="checkbox">
            <div className="checker">
              <span className={classnames({ checked: this.isSelectedAll })}>
                <input type="checkbox" value="multiselect-all" />
              </span>
            </div>
            {t('Select All')}
          </label>
        </a>
      </li>
    );
  }

  closeMenu = () => {
    if (this.state.isOpen) {
      this.handleSearchReset();
    }
    this.setState({ isOpen: false, isSelectedAll: false });
  };

  get button() {
    const { isDisabled, t, value, options, maxNumberToShow } = this.props;
    const { isOpen } = this.state;

    const flattenOptions = [].concat(...options);

    let buttonTitle = t('None selected');

    if (value?.length) {
      if (value.length > 1 && value.length === flattenOptions.length) {
        buttonTitle = t('All selected');
      } else if (value.length < flattenOptions.length) {
        const selected = [];

        _.forEach(value, v => {
          const option = flattenOptions.find(
            opt => this.getOptionValue(opt) === v,
          );
          if (option) {
            selected.push(this.getOptionTitle(option));
          }
        });

        if (selected.length > maxNumberToShow) {
          buttonTitle = `${selected.length} ${t('selected')}`;
        }

        if (selected.length) {
          buttonTitle = _.join(selected, ', ');
        }

        if (!_.isString(buttonTitle)) {
          buttonTitle = t('Some selected');
        }
      }
    }

    return (
      <button
        ref={el => (this.controlRef = el)}
        className={classnames(
          'dropdown-toggle btn btn-default',
          style.buttonOverflow,
          {
            [style.zIndex3]: isOpen,
          },
        )}
        disabled={isDisabled}
        title={buttonTitle}
        type="button"
        onClick={this.handleToggleMenu}
      >
        <span className="multiselect-selected-text">{this.buttonText}</span>
        <b className="caret" />
      </button>
    );
  }

  filterOrderGroup = options => {
    let res = options;
    const { search } = this.state;
    if (search) {
      const searchLower = search.trim().toLocaleLowerCase();
      res = _.filter(res, option => {
        const title = this.getOptionTitle(option) || '';
        return !search || title.toLocaleLowerCase().includes(searchLower);
      });
    }

    const { orderOptionsBy } = this.props;
    if (orderOptionsBy) {
      res = _.orderBy(res, orderOptionsBy);
    }

    return res && res.length ? res : null;
  };

  get options() {
    const { groupTitleKeys, options } = this.props;
    let res;

    if (groupTitleKeys) {
      res = options.map(this.filterOrderGroup).filter(g => g);
    } else {
      res = this.filterOrderGroup(options);
    }

    return res;
  }

  get menuOptions() {
    const { groupTitleKeys } = this.props;
    const options = this.options;

    if (!options || !options.length) {
      return <Empty />;
    }

    return (
      <>
        {this.selectAllButton}
        {groupTitleKeys
          ? options.map((optionsGroup, index) =>
              this.renderSelectBody(optionsGroup, groupTitleKeys[index]),
            )
          : this.renderSelectBody(options)}
      </>
    );
  }

  get menu() {
    const { isOpen } = this.state;
    if (!isOpen) {
      return null;
    }

    const { menuContainerStyle, maxHeight, dropup } = this.props;

    return (
      <div
        className={classnames('dropdown-menu open', style.fixed)}
        /* eslint-disable-next-line react/forbid-dom-props */
        style={menuContainerStyle}
      >
        {!dropup && this.liveSearchInput}
        <Scroll
          autoHeightMax={maxHeight}
          className={classnames('dropdown-menu inner')}
          classNameInner={classnames(
            'dropdown-menu inner multiselect-container',
            style.scrollable,
          )}
          isOpen={isOpen}
          tagName="ul"
        >
          {this.menuOptions}
        </Scroll>
        {dropup && this.liveSearchInput}
      </div>
    );
  }

  get label() {
    const { placeholder, isRequired } = this.props;
    const { isLabelVisible } = this.state;

    return (
      <AnimatedTitle
        isRequired={isRequired}
        placeholder={placeholder}
        value={isLabelVisible}
      />
    );
  }

  get isSelectedAll() {
    const { value, options, groupTitleKeys } = this.props;

    return groupTitleKeys
      ? value?.length === _.flatten(options)?.length
      : value?.length === options?.length;
  }

  render() {
    const {
      renderHelpBlock,
      isFilterBar,
      dropup,
      className,
      noLabel,
    } = this.props;
    const { isOpen } = this.state;

    return (
      <div
        ref={el => (this.containerRef = el)}
        className={classnames(
          'form-group-material select-picker-wrapper',
          className,
        )}
      >
        {!noLabel && this.label}
        <div
          className={classnames(
            'multi-select-full btn-group bootstrap-select form-control select-title',
            // style.zIndex3,
            {
              open: isOpen,
              dropup,
              [style.filterBarStyle]: isFilterBar,
              [style.zIndex1]: !isOpen,
            },
          )}
        >
          {this.button}
          {this.menu}
        </div>

        {renderHelpBlock && (
          <span className="help-block pull-right">{renderHelpBlock()}</span>
        )}

        {this.infoMessage}
        {this.errorMessage}
      </div>
    );
  }
}
