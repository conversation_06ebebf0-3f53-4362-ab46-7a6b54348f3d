.scrollable {
  float: none !important;
  width: auto !important;
}

.fixed {
  position: fixed !important;
  min-width: 0 !important;
}
.posUnset {
  position: unset !important;
}
.positionFix {
  :global(.dropdown-menu) {
    scrollbar-width: none !important;
  }
}

.autoWidth {
  width: auto !important;
}

.filterBarStyle {
  width: 100% !important;
  margin-left: 0 !important;
  padding: 0 !important;
}

.filterBarButtonStyle {
  margin-top: -7px !important;
  padding: 10px 20px 10px 10px !important;
  color: #777;
  border-bottom-color: transparent !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
}

.filterBarCaret {
  right: 22px !important;
  margin-right: 10px !important;
}

@media (min-width: 769px) {
  .filterBarCaret {
    right: 0 !important;
  }
}

.filterBarDropdown {
  min-width: 139px !important;
  margin-top: -5px !important;
  margin-bottom: -3px !important;
}

@media all and (min-width: 768px) {
  .filterBarDropdown {
    position: fixed !important;
  }
}
@media all and (max-width: 670px) {
  .horizontolScroll {
    width: 93% !important;
    overflow: scroll !important;
    text-align: left !important;
    display: inline-block !important;
  }
}
.buttonOverflow {
  text-overflow: ellipsis;
  overflow: hidden;
  text-align: left !important;
  padding-right: 10px !important;
}

.colorFix {
  border-color: #555555;
}

.statusAddon {
  margin: 0 0 0 10px;
}

.minHeight {
  min-height: 20px;
}

.center {
  text-align: center !important;
}

.item {
  display: inline-block;
  color: #333333;
}
.noResult {
  text-align: center;
  padding: 8px 16px;
  color: #999999;
}

.optionLabelContainer {
  display: flex;
  justify-content: space-between;
}

.zIndex3 {
  z-index: 3 !important;
}
.zIndex1 {
  z-index: 1 !important;
}